# Embedding Configuration Guide

This project now supports multiple embedding providers to give you flexibility in choosing the best option for your needs.

## Available Providers

### 1. Sentence Transformers (Recommended) 🌟

**Pros:**
- ✅ Free and runs locally
- ✅ No API key required
- ✅ Fast and reliable
- ✅ Works offline
- ✅ Good quality embeddings

**Cons:**
- ❌ Requires local compute resources
- ❌ Initial model download

**Configuration:**
```env
EMBEDDING_PROVIDER=sentence-transformers
EMBEDDING_MODEL=all-MiniLM-L6-v2
```

**Popular Models:**
- `all-MiniLM-L6-v2` - Fast, good quality (default)
- `all-mpnet-base-v2` - Higher quality, slower
- `all-distilroberta-v1` - Balanced performance

### 2. Gemini API

**Pros:**
- ✅ High-quality embeddings
- ✅ No local compute required
- ✅ Google's latest embedding model

**Cons:**
- ❌ Requires API key
- ❌ Usage costs
- ❌ Internet connection required

**Configuration:**
```env
EMBEDDING_PROVIDER=gemini
GEMINI_API_KEY=your_gemini_api_key
```

### 3. OpenAI API

**Pros:**
- ✅ High-quality embeddings
- ✅ Well-tested and reliable

**Cons:**
- ❌ Requires API key
- ❌ Usage costs
- ❌ Not available on OpenRouter

**Configuration:**
```env
EMBEDDING_PROVIDER=openai
EMBEDDING_MODEL=text-embedding-3-small
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=https://api.openai.com/v1
```

## Quick Setup

### Option 1: Use Sentence Transformers (Recommended)

1. Update your `.env` file:
```env
EMBEDDING_PROVIDER=sentence-transformers
EMBEDDING_MODEL=all-MiniLM-L6-v2
```

2. Install dependencies:
```bash
python install_embedding_deps.py
```

3. Restart your application

### Option 2: Use Gemini API

1. Get a Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)

2. Update your `.env` file:
```env
EMBEDDING_PROVIDER=gemini
GEMINI_API_KEY=your_gemini_api_key
```

3. Install dependencies:
```bash
python install_embedding_deps.py
```

4. Restart your application

## Troubleshooting

### Common Issues

1. **"sentence-transformers not installed"**
   ```bash
   pip install sentence-transformers torch
   ```

2. **"google-generativeai not installed"**
   ```bash
   pip install google-generativeai
   ```

3. **Model download issues**
   - Ensure you have internet connection for initial model download
   - Models are cached locally after first download

4. **Memory issues with sentence-transformers**
   - Try a smaller model like `all-MiniLM-L6-v2`
   - Increase system memory if possible

### Performance Tips

1. **For production**: Use `all-mpnet-base-v2` for best quality
2. **For development**: Use `all-MiniLM-L6-v2` for speed
3. **For low memory**: Use `all-MiniLM-L6-v2` or smaller models

## Migration from OpenAI

If you were previously using OpenAI embeddings and want to switch:

1. **Keep existing embeddings**: No need to regenerate if switching providers
2. **Regenerate for consistency**: Run document processing again for best results
3. **Test thoroughly**: Different embedding models may affect search quality

## Compatibility

- ✅ All existing functionality preserved
- ✅ Backward compatible with OpenAI setup
- ✅ Works with existing database schema
- ✅ No changes needed to API endpoints
