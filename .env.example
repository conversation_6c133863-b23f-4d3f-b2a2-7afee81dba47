OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=https://api.openai.com/v1

# Embedding Configuration
# Options: "sentence-transformers", "openai", "gemini"
EMBEDDING_PROVIDER=sentence-transformers
# For sentence-transformers: model name (e.g., "all-MiniLM-L6-v2", "all-mpnet-base-v2")
# For OpenAI: embedding model (e.g., "text-embedding-3-small", "text-embedding-ada-002")
# For Gemini: not used (uses text-embedding-004)
EMBEDDING_MODEL=all-MiniLM-L6-v2

# Gemini API (optional, only needed if using Gemini embeddings)
GEMINI_API_KEY=your_gemini_api_key
POSTGRES_USER=odoo_expert
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=odoo_expert_db
POSTGRES_HOST=db
POSTGRES_PORT=5432
LLM_MODEL=gpt-4o
BEARER_TOKEN=comma_separated_bearer_tokens
CORS_ORIGINS=http://localhost:3000,http://localhost:8501,https://www.odoo.com
ODOO_VERSIONS=16.0,17.0,18.0
SYSTEM_PROMPT="You are an expert in Odoo development and architecture.
Answer the question using the provided documentation chunks and conversation history.
In your answer:
1. Start with a clear, direct response to the question
2. Support your answer with specific references to the source documents
3. Use markdown formatting for readability
4. When citing information, mention which Source (1, 2, etc.) it came from
5. If different sources provide complementary information, explain how they connect
6. Consider the conversation history for context

Format your response like this:

**Answer:**
[Your main answer here]

**Sources Used:**
- Source 1: Title chunk['url']
- Source 2: Title chunk['url']
- etc if needed"

# Data Directories
RAW_DATA_DIR=raw_data
MARKDOWN_DATA_DIR=markdown
