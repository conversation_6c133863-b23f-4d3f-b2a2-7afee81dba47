:show-content:
:hide-page-toc:

=========
Tutorials
=========

.. toctree::
   tutorials/setup_guide
   tutorials/server_framework_101
   tutorials/discover_js_framework
   tutorials/master_odoo_web_framework
   tutorials/define_module_data
   tutorials/restrict_data_access
   tutorials/unit_tests
   tutorials/importable_modules
   tutorials/mixins
   tutorials/pdf_reports
   tutorials/website_theme

.. tip::
   If you are new to Odoo development, we recommend starting with the :doc:`setup guide
   <tutorials/setup_guide>`.

Learn the server and web frameworks
===================================

.. cards::

   .. card:: Server framework 101
      :target: tutorials/server_framework_101
      :tag: Beginner
      :large:

      This introductory tutorial is designed for complete beginners seeking to get started in Odoo
      development. It covers the essential aspects and key concepts of the server framework. Learn
      to create a simple module from scratch with step-by-step instructions and practical insights.

   .. card:: Discover the web framework
      :target: tutorials/discover_js_framework
      :tag: Beginner

      This tutorial will teach the basics of the web framework and how to work with Owl components
      by customizing the web client.

   .. card:: Master the web framework
      :target: tutorials/master_odoo_web_framework
      :tag: Advanced

      Become an expert in the web framework. A large variety of features are covered such as fields,
      views, and even the kitten mode.

Expand your knowledge on the server framework
=============================================

.. cards::

   .. card:: Define module data
      :target: tutorials/define_module_data
      :tag: Beginner

      Define master and demo data for an Odoo module, leveraging the strengths of the CSV and XML
      file formats to accommodate specific data requirements.

   .. card:: Restrict access to data
      :target: tutorials/restrict_data_access
      :tag: Beginner

      Implement security measures to restrict access to sensitive data with the help of groups,
      access rights, and record rules.

   .. card:: Safeguard your code with unit tests
      :target: tutorials/unit_tests
      :tag: Beginner

      Write effective unit tests in Python to ensure the resilience of your code and safeguard it
      against unexpected behaviors and regressions.

   .. card:: Write importable modules
      :target: tutorials/importable_modules

      Write modules that define new models, fields and logic using only data files.

   .. card:: Reuse code with mixins
      :target: tutorials/mixins

      Create mixins to code features once and reuse them in multiple models.

   .. card:: Build PDF reports
      :target: tutorials/pdf_reports

      Use QWeb, Odoo's powerful templating engine, to create custom PDF reports for your documents.

   .. card:: Build a website theme
      :target: tutorials/website_theme

      Create a tailored website from scratch fully integrated with Odoo and editable via the Website
      Builder.
