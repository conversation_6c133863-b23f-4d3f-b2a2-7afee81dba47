:show-content:

.. _tutorials/getting_started:

===============
Getting started
===============

.. toctree::
    :titlesonly:
    :glob:

    getting_started/*

Welcome to the Getting Started Odoo tutorial! If you reached this page that means you are
interested in the development of your own Odoo module. It might also mean that you recently
joined the Odoo company for a rather technical position. In any case, your journey to the
technical side of Odoo starts here.

The goal of this tutorial is for you to get an insight of the most important parts of the Odoo
development framework while developing your own Odoo module to manage real estate assets. The
chapters should be followed in their given order since they cover the development of a new Odoo
application from scratch in an incremental way. In other words, each chapter depends on the previous
one.

.. attention::
   Are you following this tutorial as part of your technical onboarding as an Odoo employee? Then,
   we ask you to complete all the chapters before joining your new team.

Ready? Let's get started!

* :doc:`getting_started/01_architecture`
* :doc:`getting_started/02_setup`
* :doc:`getting_started/03_newapp`
* :doc:`getting_started/04_basicmodel`
* :doc:`getting_started/05_securityintro`
* :doc:`getting_started/06_firstui`
* :doc:`getting_started/07_basicviews`
* :doc:`getting_started/08_relations`
* :doc:`getting_started/09_compute_onchange`
* :doc:`getting_started/10_actions`
* :doc:`getting_started/11_constraints`
* :doc:`getting_started/12_sprinkles`
* :doc:`getting_started/13_inheritance`
* :doc:`getting_started/14_other_module`
* :doc:`getting_started/15_qwebintro`
* :doc:`getting_started/16_final_word`
