<svg viewBox="-7.5 -8 612.203125 194" style="max-width: 100%;" aria-labelledby="chart-title-graph-div chart-desc-graph-div" role="img" xmlns="http://www.w3.org/2000/svg" width="100%" id="graph-div" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink"><style>@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css");'</style><title id="chart-title-graph-div"></title><desc id="chart-desc-graph-div"></desc><style>#graph-div {font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#graph-div .error-icon{fill:#552222;}#graph-div .error-text{fill:#552222;stroke:#552222;}#graph-div .edge-thickness-normal{stroke-width:2px;}#graph-div .edge-thickness-thick{stroke-width:3.5px;}#graph-div .edge-pattern-solid{stroke-dasharray:0;}#graph-div .edge-pattern-dashed{stroke-dasharray:3;}#graph-div .edge-pattern-dotted{stroke-dasharray:2;}#graph-div .marker{fill:#333333;stroke:#333333;}#graph-div .marker.cross{stroke:#333333;}#graph-div svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#graph-div .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#graph-div .cluster-label text{fill:#333;}#graph-div .cluster-label span{color:#333;}#graph-div .label text,#graph-div span{fill:#333;color:#333;}#graph-div .node rect,#graph-div .node circle,#graph-div .node ellipse,#graph-div .node polygon,#graph-div .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#graph-div .node .label{text-align:center;}#graph-div .node.clickable{cursor:pointer;}#graph-div .arrowheadPath{fill:#333333;}#graph-div .edgePath .path{stroke:#333333;stroke-width:2.0px;}#graph-div .flowchart-link{stroke:#333333;fill:none;}#graph-div .edgeLabel{background-color:#e8e8e8;text-align:center;}#graph-div .edgeLabel rect{opacity:0.5;background-color:#e8e8e8;fill:#e8e8e8;}#graph-div .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#graph-div .cluster text{fill:#333;}#graph-div .cluster span{color:#333;}#graph-div div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#graph-div :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="10" viewBox="0 0 10 10" class="marker flowchart" id="flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="0" viewBox="0 0 10 10" class="marker flowchart" id="flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-odoo LE-Owl" id="L-odoo-Owl-0" d="M298.1015625,39L298.1015625,43.166666666666664C298.1015625,47.333333333333336,298.1015625,55.666666666666664,298.1015625,64C298.1015625,72.33333333333333,298.1015625,80.66666666666667,298.1015625,84.83333333333333L298.1015625,89"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(-7.5, 81)" class="root"><g class="clusters"><g id="Owl" class="cluster default"><rect height="89" width="596.203125" y="8" x="8" ry="0" rx="0" style=""></rect><g transform="translate(292.3984375, 13)" class="cluster-label"><foreignObject height="24" width="27.40625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Owl</span></div></foreignObject></g></g></g><g class="edgePaths"></g><g class="edgeLabels"></g><g class="nodes"><g transform="translate(91.25, 52.5)" id="flowchart-C-7599" class="node default default"><rect height="39" width="96.5" y="-19.5" x="-48.25" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-40.75, -12)" style="" class="label"><foreignObject height="24" width="81.5"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Component</span></div></foreignObject></g></g><g transform="translate(230.2109375, 52.5)" id="flowchart-T-7600" class="node default default"><rect height="39" width="81.421875" y="-19.5" x="-40.7109375" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-33.2109375, -12)" style="" class="label"><foreignObject height="24" width="66.421875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Template</span></div></foreignObject></g></g><g transform="translate(346.28125, 52.5)" id="flowchart-H-7601" class="node default default"><rect height="39" width="50.71875" y="-19.5" x="-25.359375" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-17.859375, -12)" style="" class="label"><foreignObject height="24" width="35.71875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Hook</span></div></foreignObject></g></g><g transform="translate(442.8125, 52.5)" id="flowchart-S-7602" class="node default default"><rect height="39" width="42.34375" y="-19.5" x="-21.171875" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-13.671875, -12)" style="" class="label"><foreignObject height="24" width="27.34375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Slot</span></div></foreignObject></g></g><g transform="translate(541.59375, 52.5)" id="flowchart-E-7603" class="node default default"><rect height="39" width="55.21875" y="-19.5" x="-27.609375" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-20.109375, -12)" style="" class="label"><foreignObject height="24" width="40.21875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Event</span></div></foreignObject></g></g></g></g><g transform="translate(298.1015625, 19.5)" id="flowchart-odoo-7604" class="node default default"><rect height="39" width="212.765625" y="-19.5" x="-106.3828125" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-98.8828125, -12)" style="" class="label"><foreignObject height="24" width="197.765625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Odoo JavaScript framework</span></div></foreignObject></g></g></g></g></g></svg>