:show-content:

====================
Server framework 101
====================

.. toctree::
    :titlesonly:
    :glob:

    server_framework_101/*

Welcome to the Server framework 101 tutorial! If you reached this page that means you are
interested in the development of your own Odoo module. It might also mean that you recently
joined the Odoo company for a rather technical position. In any case, your journey to the
technical side of Odoo starts here.

The goal of this tutorial is for you to get an insight of the most important parts of the Odoo
development framework while developing your own Odoo module to manage real estate assets. The
chapters should be followed in their given order since they cover the development of a new Odoo
application from scratch in an incremental way. In other words, each chapter depends on the previous
one.

.. important::
   Before going further, make sure you have prepared your development environment with the
   :doc:`setup guide <setup_guide>`.

Ready? Let's get started!

* :doc:`server_framework_101/01_architecture`
* :doc:`server_framework_101/02_newapp`
* :doc:`server_framework_101/03_basicmodel`
* :doc:`server_framework_101/04_securityintro`
* :doc:`server_framework_101/05_firstui`
* :doc:`server_framework_101/06_basicviews`
* :doc:`server_framework_101/07_relations`
* :doc:`server_framework_101/08_compute_onchange`
* :doc:`server_framework_101/09_actions`
* :doc:`server_framework_101/10_constraints`
* :doc:`server_framework_101/11_sprinkles`
* :doc:`server_framework_101/12_inheritance`
* :doc:`server_framework_101/13_other_module`
* :doc:`server_framework_101/14_qwebintro`
* :doc:`server_framework_101/15_final_word`
