:show-content:
:hide-page-toc:

=============
How-to guides
=============

.. toctree::
   howtos/scss_tips
   howtos/javascript_field
   howtos/javascript_view
   howtos/javascript_client_action
   howtos/standalone_owl_application
   howtos/frontend_owl_components
   howtos/website_themes

   howtos/web_services
   howtos/company
   howtos/create_reports
   howtos/accounting_localization
   howtos/translations
   howtos/connect_device

   howtos/upgrade_custom_db

Frontend development
====================

.. cards::

   .. card:: Write lean easy-to-maintain CSS
      :target: howtos/scss_tips

      Follow this guide to keep the technical debt of your CSS code under control.

   .. card:: Customize a field
      :target: howtos/javascript_field

      Learn how to customize field components in the web framework.

   .. card:: Customize a view type
      :target: howtos/javascript_view

      Learn how to customize view types in the web framework.

   .. card:: Create a client action
      :target: howtos/javascript_client_action

      Learn how to create client actions in the web framework.

   .. card:: Create a standalone Owl application
      :target: howtos/standalone_owl_application

      Learn how to create a public-facing Owl application outside of the web client using a
      controller and the web framework.

   .. card:: Use Owl components on the portal and website
      :target: howtos/frontend_owl_components

      Learn how to use Owl components on the portal and website.

   .. card:: Website themes
      :target: howtos/website_themes

      Learn how to customize your website by creating a custom theme.

Server-side development
=======================

.. cards::

   .. card:: Web services
      :target: howtos/web_services

      Learn more about Odoo's web services.

   .. card:: Multi-company guidelines
      :target: howtos/company

      Learn how to manage multiple companies and deal with the records-related specificities of a
      multi-company environment.

   .. card:: Create customized reports
      :target: howtos/create_reports

      Learn how to create customized reports with SQL Views.

   .. card:: Accounting localization
      :target: howtos/accounting_localization

      Learn how to build a localization module, create bank operation models and dynamic reports.

   .. card:: Translating modules
      :target: howtos/translations

      Learn how to provide translation abilities to your module.

   .. card:: Connect with a device
      :target: howtos/connect_device

      Learn how to enable a module to detect and communicate with an IoT device.

Custom development
==================

.. cards::

   .. card:: Upgrade a customized database
      :target: howtos/upgrade_custom_db

      Learn how to upgrade a customized database, as well as the code and data of its custom
      modules.
