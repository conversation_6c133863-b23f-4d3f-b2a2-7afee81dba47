<svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 660 230" width="660" height="230">
<defs>
  <pattern id="diagonalHatch" width="5" height="5" patternTransform="rotate(45 0 0)" patternUnits="userSpaceOnUse">
    <line x1="0" y1="0" x2="0" y2="5" stroke="#cccccc" stroke-width="1"/>
  </pattern>
</defs>
<g font-family="Arial" font-size="16px" direction="ltr" fill="none" stroke="#495057" stroke-width="0">
  <text x="14" y="31" fill="#888888" style="font-weight: bold;">Column 1</text>

  <rect x="10" y="40" rx="3" ry="3" width="144" height="80" fill="url(#diagonalHatch)" stroke-width="1"/>
  <text x="14" y="60" fill="#888888">Name 1</text>

  <rect x="10" y="130" rx="3" ry="3" width="144" height="80" fill="url(#diagonalHatch)" stroke-width="1"/>
  <text x="14" y="150" fill="#888888">Name 3</text>

  <text x="174" y="31" fill="#888888" style="font-weight: bold;">Column 2</text>

  <rect x="170" y="40" rx="3" ry="3" width="144" height="80" fill="url(#diagonalHatch)" stroke-width="1"/>
  <text x="174" y="60" fill="#888888">Name 2</text>

  <text x="334" y="31" fill="#888888" style="font-weight: bold;">Column 2</text>
  <rect x="330" y="40" rx="3" ry="3" width="144" height="80" fill="url(#diagonalHatch)" stroke-width="1"/>
  <text x="334" y="60" fill="#888888">Name 4</text>
</g>
</svg>