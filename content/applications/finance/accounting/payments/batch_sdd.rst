=======================================
Batch payments: SEPA Direct Debit (SDD)
=======================================

SEPA, the Single Euro Payments Area, is a payment-integration initiative of the European Union for
simplification of bank transfers denominated in euros. With **SEPA Direct Debit** (SDD), your
customers can sign a mandate that authorizes you to collect future payments from their bank
accounts. This is particularly useful for recurring payments based on a subscription.

You can record customer mandates in Odoo, and generate `.xml` files containing pending payments made
with an SDD mandate.

.. note::
   | SDD is supported by all SEPA countries, which includes the 27 member states of the European
     Union as well as additional countries.
   | `List of all SEPA countries
     <https://www.europeanpaymentscouncil.eu/document-library/other/epc-list-sepa-scheme-countries>`_.

Configuration
=============

Go to :menuselection:`Accounting app --> Configuration --> Settings`, activate
:guilabel:`SEPA Direct Debit (SDD)`, and click on :guilabel:`Save`. Enter your company's
:guilabel:`Creditor Identifier`. This number is provided by your bank institution, or the authority
responsible for delivering them.

.. image:: batch_sdd/creditor-identifier.png
   :align: center
   :alt: Add a SEPA Creditor Identifier to Odoo Accounting

SEPA Direct Debit Mandates
==========================

Create a mandate
----------------

The :abbr:`SDD (sepa direct debit)` Mandate is the document that your customers sign to authorize
you to collect money directly from their bank accounts.

To create a new mandate, go to :menuselection:`Accounting app --> Customers -->
Direct Debit Mandates`, on :guilabel:`Create`, and fill out the form. Export the PDF file by
clicking on :guilabel:`Print`. It is then up to your customer to sign this document. Once done,
upload the signed file and click on :guilabel:`Validate` to start running the mandate.

.. important::
   Make sure that the **IBAN bank accounts details** are correctly recorded on the debtor’s contact
   form, under the :guilabel:`Accounting` tab, and in your own :doc:`Bank Account <../bank>` settings.

SEPA Direct Debit as a payment method
-------------------------------------

SDD can be used as a payment method both on your **eCommerce** or on the **Customer Portal** by
activating SDD as a **Payment Provider**. With this method, your customers can create and sign their
mandates themselves.

To do so, go to :menuselection:`Accounting --> Configuration --> Payment Providers`, click on *SEPA
Direct Debit*, and set it up according to your needs.
To do so, go to :menuselection:`Accounting app --> Configuration --> Payment Acquirers`, click on
:guilabel:`SEPA Direct Debit`.

.. important::
   Make sure to change the :guilabel:`State` field to :guilabel:`Enabled`, and to check
   :guilabel:`Online Signature`, as this is necessary to let your customers sign their mandates.

Customers using SDD as payment method get prompted to add their IBAN, email address, and to sign
their SEPA Direct Debit mandate.

.. todo::
   Add link to future **Payment Provider** documentation.

Close or revoke a mandate
-------------------------

Direct debit mandates are closed automatically after their :guilabel:`End Date`. If this field is
left blank, the mandate keeps being **Active** until it is **Closed** or **Revoked**.

Clicking on :guilabel:`Close` updates the mandate’s end day to the current day. This means that
invoices issued after the present day will not be processed with an SDD payment.

Clicking on :guilabel:`Revoke` disables the mandate immediately. No SDD payment can be registered
anymore, regardless of the invoice’s date. However, payments that have already been registered are
still included in the next SDD `.xml` file.

.. warning::
   Once a mandate has been :guilabel:`closed` or :guilabel:`revoked`, it cannot be reactivated.

Get paid with SEPA Direct Debit batch payments
==============================================

Customer invoices
-----------------

You can register SDD payments for invoices issued to customers who have an active SDD mandate.

To do so, open the invoice, click on :guilabel:`Register Payment`, and choose
:guilabel:`SEPA Direct Debit` as payment method.

Generate SEPA Direct Debit `.XML` files to submit payments
----------------------------------------------------------

`.xml` files with all SDD payment instructions can be uploaded to your online banking interface
to process all payments at once.

.. note::
   The files generated by Odoo follow the SEPA Direct Debit **PAIN.008.001.02** specifications, as
   required by the SEPA customer-to-bank `implementation guidelines
   <https://www.europeanpaymentscouncil.eu/document-library/implementation-guidelines/sepa-credit-transfer-customer-psp-implementation>`_,
   which ensures compatibility with the banks.

To generate your `.xml` file of multiple pending SDD payments, you can create a batch payment.
To do so, go to :menuselection:`Accounting app --> Customers --> Payments`, select the needed
payments, then click on :guilabel:`Action`, and finally :guilabel:`Create a batch Payment`. Once
you click on :guilabel:`Validate`, the `.xml` file is directly available for download.

.. image:: batch_sdd/xml.png
   :align: center
   :alt: Generate an .XML file for your SDD payments in Odoo Accounting

Finally, upload this file to your online banking interface to process the payments.

.. tip::
   You can retrieve all the generated SDD `.xml` files by going to
   :menuselection:`Accounting app --> Customers --> Batch Payments`.

.. seealso::
   * :doc:`batch`
   * :doc:`../bank`
   * `List of all SEPA countries
     <https://www.europeanpaymentscouncil.eu/document-library/other/epc-list-sepa-scheme-countries>`_
   * `Sepa guidelines
     <https://www.europeanpaymentscouncil.eu/document-library/implementation-guidelines/sepa-credit-transfer-inter-psp-implementation-guidelines>`_
