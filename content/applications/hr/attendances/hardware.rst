========
Hardware
========

Kiosk management
================

A kiosk is a self-service station that allows employees to check in and check out for work shifts.

There are two ways to set up a kiosk:

- **Laptop and desktop PC**

  Running a kiosk in a web browser is the cheapest and most flexible option. You can print employee
  badges with any thermal or inkjet printer compatible with your web browser.

- **Tablet and mobile phone (Android or iOS)**

  Tablets and mobile phones take up much less space, and their touchscreens are easy to use.
  Consider putting them in a secure stand at the front desk or mounting them securely on a wall.

  .. tip::
     We recommend using an iPad together with the `Heckler Design WindFall Stand
     <https://hecklerdesign.com/products/windfall-stand-for-ipad>`_

RFID key fob readers
====================

Employees can scan personal RFID key fobs with an RFID reader to manage check-ins and check-outs
quickly and easily.

.. image:: hardware/rfid-reader.jpg
   :align: center
   :width: 40%
   :alt: An RFID key fob is placed on an RFID reader

.. tip::
   We recommend using the `Neuftech USB RFID Reader <https://neuftech.net/Neuftech-USB-RFID-Reader-ID-Kartenleseger%C3%A4t-Kartenleser-Kontaktlos-Card-Reader-f%C3%BCr-EM4100>`_.

.. note::
   An IoT box is **not** required.

Barcode scanners
================

Employees can scan the barcode on their employee badges to manage check-ins and check-outs quickly
and easily. The kiosk mode works with most USB barcode scanners connected directly to a computer.
Bluetooth barcode scanners are also supported natively.

.. tip::
   We recommend using the `Honeywell product line
   <https://sps.honeywell.com/us/en/products/productivity/barcode-scanners>`_. If the barcode
   scanner is connected directly to a computer, it must be configured to use the computer's keyboard
   layout.

.. note::
   An IoT box is **not** required.
