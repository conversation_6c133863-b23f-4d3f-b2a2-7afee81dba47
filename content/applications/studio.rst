:show-content:
:hide-page-toc:

======
Studio
======

.. toctree::
   :titlesonly:

   studio/fields
   studio/views
   studio/models_modules_apps
   studio/automated_actions
   studio/pdf_reports
   studio/approval_rules

Studio is a toolbox that allows you to customize Odoo without coding knowledge. For example, you
can, in any app, add or modify:

- :doc:`Fields <studio/fields>`
- :doc:`Views <studio/views>`
- :doc:`Models <studio/models_modules_apps>`
- :doc:`Automation rules <studio/automated_actions>`
- :doc:`PDF reports <studio/pdf_reports>`
- :doc:`Approval rules <studio/approval_rules>`
- Security rules

You can also :doc:`build an app from scratch <studio/models_modules_apps>`.

.. _studio/access:

To access Studio, navigate to the app and model you want to modify, then click the **Toggle Studio**
button, or vice versa.

To close Studio, click :guilabel:`Close` in the upper right corner.

.. seealso::
   `Odoo Tutorials: Studio <https://www.odoo.com/slides/studio-31>`_
