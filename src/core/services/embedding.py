from typing import List, Optional
from openai import Async<PERSON>penAI
from src.utils.logging import logger
from src.config.settings import settings
import asyncio
import functools

class EmbeddingService:
    def __init__(self, client: Optional[AsyncOpenAI] = None):
        self.client = client
        self.provider = settings.EMBEDDING_PROVIDER.lower()
        self.model_name = settings.EMBEDDING_MODEL
        self._sentence_transformer = None
        self._gemini_client = None

        # Initialize the appropriate provider
        self._initialize_provider()

    def _initialize_provider(self):
        """Initialize the embedding provider based on configuration."""
        if self.provider == "sentence-transformers":
            self._initialize_sentence_transformers()
        elif self.provider == "gemini":
            self._initialize_gemini()
        elif self.provider == "openai":
            if not self.client:
                raise ValueError("OpenAI client is required for OpenAI embeddings")
        else:
            raise ValueError(f"Unsupported embedding provider: {self.provider}")

    def _initialize_sentence_transformers(self):
        """Initialize sentence-transformers model."""
        try:
            from sentence_transformers import SentenceTransformer
            self._sentence_transformer = SentenceTransformer(self.model_name)
            logger.info(f"Initialized sentence-transformers with model: {self.model_name}")
        except ImportError:
            logger.error("sentence-transformers not installed. Install with: pip install sentence-transformers")
            raise
        except Exception as e:
            logger.error(f"Error initializing sentence-transformers: {e}")
            raise

    def _initialize_gemini(self):
        """Initialize Gemini client."""
        try:
            import google.generativeai as genai
            if not settings.GEMINI_API_KEY:
                raise ValueError("GEMINI_API_KEY is required for Gemini embeddings")
            genai.configure(api_key=settings.GEMINI_API_KEY)
            self._gemini_client = genai
            logger.info("Initialized Gemini embeddings")
        except ImportError:
            logger.error("google-generativeai not installed. Install with: pip install google-generativeai")
            raise
        except Exception as e:
            logger.error(f"Error initializing Gemini: {e}")
            raise

    async def get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using the configured provider."""
        try:
            text = text.replace("\n", " ")
            if len(text) > 8000:
                text = text[:8000] + "..."

            if self.provider == "sentence-transformers":
                return await self._get_sentence_transformer_embedding(text)
            elif self.provider == "gemini":
                return await self._get_gemini_embedding(text)
            elif self.provider == "openai":
                return await self._get_openai_embedding(text)
            else:
                raise ValueError(f"Unsupported provider: {self.provider}")

        except Exception as e:
            logger.error(f"Error getting embedding with {self.provider}: {e}")
            raise

    async def _get_sentence_transformer_embedding(self, text: str) -> List[float]:
        """Get embedding using sentence-transformers."""
        # Run in thread pool to avoid blocking the event loop
        loop = asyncio.get_event_loop()
        embedding = await loop.run_in_executor(
            None,
            functools.partial(self._sentence_transformer.encode, text)
        )
        return embedding.tolist()

    async def _get_gemini_embedding(self, text: str) -> List[float]:
        """Get embedding using Gemini API."""
        try:
            # Use text-embedding-004 model for Gemini
            result = self._gemini_client.embed_content(
                model="models/text-embedding-004",
                content=text
            )
            return result['embedding']
        except Exception as e:
            logger.error(f"Error with Gemini embedding: {e}")
            raise

    async def _get_openai_embedding(self, text: str) -> List[float]:
        """Get embedding using OpenAI API."""
        response = await self.client.embeddings.create(
            model=self.model_name,  # Use configured model
            input=text
        )
        return response.data[0].embedding