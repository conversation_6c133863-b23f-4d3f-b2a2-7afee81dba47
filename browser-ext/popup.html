<!DOCTYPE html>
<html>
<head>
  <title>Odoo Expert Settings</title>
  <style>
    body {
      width: 300px;
      padding: 15px;
      font-family: Arial, sans-serif;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"] {
      width: 100%;
      padding: 8px;
      margin-bottom: 5px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    button {
      width: 100%;
      padding: 10px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #45a049;
    }
    .status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      display: none;
    }
    .success {
      background-color: #dff0d8;
      color: #3c763d;
    }
    .error {
      background-color: #f2dede;
      color: #a94442;
    }
    .repo-link {
      display: block;
      margin-top: 15px;
      text-align: center;
      color: #666;
      font-size: 12px;
      text-decoration: none;
    }
    .repo-link:hover {
      text-decoration: underline;
      color: #4CAF50;
    }
  </style>
</head>
<body>
  <h2>Odoo Expert Settings</h2>
  <form id="settings-form">
    <div class="form-group">
      <label for="apiUrl">API URL:</label>
      <input type="text" id="apiUrl" placeholder="e.g., http://localhost:8000/api/stream">
    </div>
    <div class="form-group">
      <label for="bearerToken">Bearer Token:</label>
      <input type="text" id="bearerToken" placeholder="Enter your bearer token">
    </div>
    <button type="submit">Save Settings</button>
  </form>
  <div id="status" class="status"></div>
  <a href="https://github.com/MFYDev/odoo-expert/" target="_blank" class="repo-link">GitHub Repo: https://github.com/MFYDev/odoo-expert</a>
  <script src="popup.js"></script>
</body>
</html>
