# Core dependencies
fastapi>=0.100.0,<1.0.0
uvicorn>=0.24.0
python-dotenv>=1.0.0
streamlit>=1.30.0

# OpenAI and LLM related
openai>=1.0.0,<2.0.0
anthropic>=0.3.0
langchain>=0.0.300
langchain-core>=0.1.0
pydantic>=2.4.2,<3.0.0
pydantic-settings>=2.0.0

# Embedding models
sentence-transformers>=2.2.0
torch>=2.0.0
google-generativeai>=0.3.0  # For Gemini embeddings (optional)

# Database and storage
psycopg[binary]>=3.1.0
psycopg-pool>=3.2.0
psutil>=5.9.0

# Document processing
pandoc>=2.3
pypandoc>=1.11
markdown-it-py>=2.2.0
langchain-text-splitters>=0.0.1
beautifulsoup4>=4.12.0

# Utilities
python-magic>=0.4.27
aiohttp>=3.8.0
requests>=2.31.0
PyYAML>=6.0.1
tenacity>=8.2.0
tqdm>=4.65.0
rich>=13.4.2

# Development dependencies
pytest>=7.4.0